export type MirrorPosition = 'below' | 'above' | 'left' | 'right';

export interface MirrorFlipOptions {
  position: MirrorPosition;
  offset: number; // 0-100 pixels
  opacity: number; // 0-100 percentage
}

/**
 * Creates a mirror flip effect on an image using HTML5 Canvas
 */
export async function createMirrorFlip(
  imageFile: File,
  options: MirrorFlipOptions
): Promise<{ url: string; file: File }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      try {
        const result = processImageWithMirrorFlip(img, options);
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(imageFile);
  });
}

function processImageWithMirrorFlip(
  img: HTMLImageElement,
  options: MirrorFlipOptions
): { url: string; file: File } {
  const { position, offset, opacity } = options;
  
  // Calculate canvas dimensions based on position
  let canvasWidth: number;
  let canvasHeight: number;
  
  if (position === 'left' || position === 'right') {
    canvasWidth = img.width * 2 + offset;
    canvasHeight = img.height;
  } else {
    canvasWidth = img.width;
    canvasHeight = img.height * 2 + offset;
  }
  
  const canvas = document.createElement('canvas');
  canvas.width = canvasWidth;
  canvas.height = canvasHeight;
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }
  
  // Clear canvas with transparent background
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);
  
  // Draw original image
  let originalX = 0;
  let originalY = 0;
  
  if (position === 'right') {
    originalX = img.width + offset;
  } else if (position === 'below') {
    originalY = img.height + offset;
  }
  
  ctx.drawImage(img, originalX, originalY);
  
  // Draw mirrored image
  ctx.save();
  ctx.globalAlpha = opacity / 100;
  
  let mirrorX = 0;
  let mirrorY = 0;
  let scaleX = 1;
  let scaleY = 1;
  
  switch (position) {
    case 'below':
      mirrorX = 0;
      mirrorY = img.height + offset + img.height;
      scaleY = -1;
      break;
    case 'above':
      mirrorX = 0;
      mirrorY = 0;
      scaleY = -1;
      originalY = img.height + offset;
      break;
    case 'right':
      mirrorX = img.width + offset + img.width;
      mirrorY = 0;
      scaleX = -1;
      break;
    case 'left':
      mirrorX = 0;
      mirrorY = 0;
      scaleX = -1;
      originalX = img.width + offset;
      break;
  }
  
  // Apply transformation for mirroring
  ctx.scale(scaleX, scaleY);
  ctx.drawImage(img, mirrorX, mirrorY);
  
  ctx.restore();
  
  // If we moved the original image, redraw it in the correct position
  if (position === 'above' || position === 'left') {
    ctx.drawImage(img, originalX, originalY);
  }
  
  // Convert canvas to blob and create file
  const dataUrl = canvas.toDataURL('image/png');
  const blob = dataURLToBlob(dataUrl);
  const file = new File([blob], 'mirror-flipped-image.png', { type: 'image/png' });
  
  return { url: dataUrl, file };
}

/**
 * Creates a simple horizontal flip (mirror) of an image
 */
export async function createHorizontalFlip(imageFile: File): Promise<{ url: string; file: File }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          throw new Error('Failed to get canvas context');
        }
        
        // Flip horizontally
        ctx.scale(-1, 1);
        ctx.drawImage(img, -img.width, 0);
        
        const dataUrl = canvas.toDataURL('image/png');
        const blob = dataURLToBlob(dataUrl);
        const file = new File([blob], 'horizontally-flipped-image.png', { type: 'image/png' });
        
        resolve({ url: dataUrl, file });
      } catch (error) {
        reject(error);
      }
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(imageFile);
  });
}

/**
 * Creates a vertical flip of an image
 */
export async function createVerticalFlip(imageFile: File): Promise<{ url: string; file: File }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          throw new Error('Failed to get canvas context');
        }
        
        // Flip vertically
        ctx.scale(1, -1);
        ctx.drawImage(img, 0, -img.height);
        
        const dataUrl = canvas.toDataURL('image/png');
        const blob = dataURLToBlob(dataUrl);
        const file = new File([blob], 'vertically-flipped-image.png', { type: 'image/png' });
        
        resolve({ url: dataUrl, file });
      } catch (error) {
        reject(error);
      }
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(imageFile);
  });
}

/**
 * Converts a data URL to a Blob
 */
function dataURLToBlob(dataURL: string): Blob {
  const arr = dataURL.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new Blob([u8arr], { type: mime });
}
