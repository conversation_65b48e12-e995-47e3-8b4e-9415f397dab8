import {
  Rows,
  Text,
  FileInput,
  SegmentedControl,
  Button,
  ProgressBar,
  Alert,
  FormField,
  FileInputItem,
  Title,
  Box,
  ReloadIcon,
  Slider,
} from "@canva/app-ui-kit";
import { useEffect, useMemo, useRef, useState } from "react";
import type {
  ContentDraft,
  ImageRef,
  ImageElementAtPoint
} from "@canva/design";
import { addElementAtPoint, selection } from "@canva/design";
import { useMutation } from "@tanstack/react-query";
import styles from "styles/components.css";
import type { ImageMimeType } from "@canva/asset";
import { getTemporaryUrl, upload } from "@canva/asset";
import ReactCompareImage from "react-compare-image";
import { createMir<PERSON>rFlip, createHorizontalFlip, createVerticalFlip, type MirrorPosition } from "../utils/mirror_flip";

const maxImageSize = 2500 * 2500 * 2;
async function fileToDataUrl(file: Blob) {
  return new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result as string);
    };
    reader.readAsDataURL(file);
  });
}

async function getImagePixels(file: Blob) {
  return new Promise<{ pixels: number; width: number; height: number }>(
    (resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          pixels: img.width * img.height,
          width: img.width,
          height: img.height,
        });
      };
      img.src = URL.createObjectURL(file);
    }
  );
}

async function readCanvaNativeImageURL(url: string): Promise<File> {
  const res = await fetch(url);
  const formatMatch = url.match(/format:([A-Z]+)/);
  const ext = formatMatch ? formatMatch[1].toLowerCase() : "png";
  return new File([await res.blob()], `selected-image.${ext}`, {
    type: `image/${ext}`,
  });
}

export const App = () => {
  const [[file], setFiles] = useState<File[]>([]);
  const [imageSourceType, setImageSourceType] = useState<
    "upload" | "content" | "unknown"
  >("unknown");
  const [contentDraft, setContentDraft] = useState<ContentDraft<{
    ref: ImageRef;
  }> | null>(null);
  const [mirrorPosition, setMirrorPosition] = useState<MirrorPosition>("below");
  const [mirrorOffset, setMirrorOffset] = useState(20);
  const [mirrorOpacity, setMirrorOpacity] = useState(50);
  const [flipType, setFlipType] = useState<"mirror" | "horizontal" | "vertical">("mirror");
  const [originImageURL, setOriginImageURL] = useState("");
  const [imagePixels, setImagePixels] = useState(0);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [hasSelect, setHasSelect] = useState(false);

  const {
    data: processedData,
    mutateAsync,
    isPending: processing,
    error: processImageError,
    reset: resetProcessImage,
  } = useMutation({
    mutationFn: async ({
      file,
      flipType,
      mirrorPosition,
      mirrorOffset,
      mirrorOpacity,
    }: {
      file: File;
      flipType: "mirror" | "horizontal" | "vertical";
      mirrorPosition: MirrorPosition;
      mirrorOffset: number;
      mirrorOpacity: number;
    }) => {
      setUploadProgress(0);

      // Simulate progress for better UX
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(interval);
            return prev;
          }
          return Math.min(prev + 10, 90);
        });
      }, 100);

      try {
        let result;

        if (flipType === "horizontal") {
          result = await createHorizontalFlip(file);
        } else if (flipType === "vertical") {
          result = await createVerticalFlip(file);
        } else {
          result = await createMirrorFlip(file, {
            position: mirrorPosition,
            offset: mirrorOffset,
            opacity: mirrorOpacity,
          });
        }

        clearInterval(interval);
        setUploadProgress(100);

        return result;
      } catch (error) {
        clearInterval(interval);
        throw error;
      }
    },
  });
  const processedUrl = processedData?.url;

  const stateRef = useRef({ imageSourceType, processing, processedUrl });

  stateRef.current = {
    imageSourceType,
    processing,
    processedUrl,
  };

  useEffect(() => {
    return selection.registerOnChange({
      scope: "image",
      async onChange(event) {
        const draft = await event.read();
        const ref = draft.contents[0]?.ref;
        setHasSelect(!!ref);
        const { imageSourceType, processedUrl, processing } = stateRef.current;
        if (imageSourceType === "upload" || processedUrl || processing) {
          return;
        }

        setContentDraft(draft);
        if (ref) {
          setImageSourceType("content");
          const { url } = await getTemporaryUrl({
            type: 'image',
            ref,
          });

          const file = await readCanvaNativeImageURL(url);
          setFiles([file]);
        } else if (imageSourceType === "content" && !processing) {
          resetData();
        }
      },
    });
  }, []);

  useEffect(() => {
    if (!file || !FileReader) {
      return;
    }

    fileToDataUrl(file).then(setOriginImageURL);
    getImagePixels(file).then(({ pixels }) => setImagePixels(pixels));
  }, [file]);

  const {
    mutate: acceptImage,
    reset: resetAcceptImage,
    data: acceptResult,
    error
  } = useMutation({
    mutationKey: [],
    mutationFn: async ({processedUrl, file, hasSelect}: {
      processedUrl: string,
      file: File,
      hasSelect: boolean
    }) => {
      if (
        contentDraft?.contents.length &&
        imageSourceType === "content" && hasSelect) {
        const asset = await upload({
          type: 'image',
          url: processedUrl,
          thumbnailUrl: processedUrl,
          mimeType: 'image/png' as ImageMimeType,
          parentRef: contentDraft.contents[0].ref,
          aiDisclosure: 'app_generated'
        });

        contentDraft.contents[0].ref = asset.ref;
        await contentDraft.save();
        return "replaced";
      } else {
        await addElementAtPoint({
          type: 'image',
          dataUrl: processedUrl,
        } as ImageElementAtPoint);
        return "added";
      }
    },
  });

  const flipTypeOptions = useMemo(() => {
    return [
      { value: "mirror" as const, label: "Mirror Reflection" },
      { value: "horizontal" as const, label: "Horizontal Flip" },
      { value: "vertical" as const, label: "Vertical Flip" },
    ];
  }, []);

  const positionOptions = useMemo(() => {
    return [
      { value: "below" as const, label: "Below" },
      { value: "above" as const, label: "Above" },
      { value: "left" as const, label: "Left" },
      { value: "right" as const, label: "Right" },
    ];
  }, []);

  const resetData = () => {
    setFiles([]);
    setMirrorPosition("below");
    setMirrorOffset(20);
    setMirrorOpacity(50);
    setFlipType("mirror");
    setOriginImageURL("");
    resetProcessImage();
    setImageSourceType("unknown");
    resetAcceptImage();
  };

  const isFileExceeded = file?.size > 1024 * 1024 * 10; // 10MB for mirror flip

  if (processing) {
    return (
      <Box
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        display="flex"
        className={styles.scrollContainer}
        paddingEnd="2u"
      >
        <Rows spacing="2u">
          <Title size="small" alignment="center">
            Creating mirror effect
          </Title>
          <ProgressBar value={uploadProgress} />
          <Text alignment="center" size="small" tone="tertiary">
            Please wait, this should only take a few moments
          </Text>
          <Button onClick={resetData} variant="secondary">
            Cancel
          </Button>
        </Rows>
      </Box>
    );
  }

  return (
    <div className={styles.scrollContainer}>
      {processedUrl ? (
        <Rows spacing="2u">
          <>
            <Rows spacing="1u">
              {!!acceptResult && (
                <Alert tone="positive"
                onDismiss={resetAcceptImage}
                >
                  <Text variant="bold">
                    {acceptResult === "added"
                      ? "Image added to design"
                      : "Image replaced"}
                  </Text>
                </Alert>
              )}

              <Text variant="bold" size="medium">
                Preview
              </Text>

              <div className={styles.imageCompareContainer}>
                <ReactCompareImage
                  sliderLineColor=""
                  leftImage={originImageURL}
                  rightImage={processedUrl}
                  leftImageLabel="Original"
                  rightImageLabel="Mirrored"
                />
              </div>
            </Rows>

            <Rows spacing="1u">
              <Button
                variant="primary"
                onClick={() => acceptImage({ processedUrl, file, hasSelect })}
              >
                {imageSourceType === "upload" || !hasSelect
                  ? "Add to design"
                  : "Replace"}
              </Button>
              <Button variant="secondary" onClick={resetData} icon={ReloadIcon}>
                Go back
              </Button>
            </Rows>
          </>
        </Rows>
      ) : (
        <Rows spacing="2u">
          <>
            <FormField
              description={
                originImageURL
                  ? ""
                  : "Upload an image or select one in your design to create mirror effects"
              }
              label="Original image"
              control={(props) =>
                originImageURL ? (
                  <>
                    {/* eslint-disable-next-line react/forbid-elements */}
                    <img src={originImageURL} className={styles.originImage} />

                    {imageSourceType === "upload" && (
                      <FileInputItem
                        onDeleteClick={() => {
                          resetData();
                        }}
                        label={file?.name}
                      />
                    )}
                  </>
                ) : (
                  <FileInput
                    {...props}
                    accept={[
                      "image/png",
                      "image/jpeg",
                      "image/jpg",
                      "image/webp",
                    ]}
                    stretchButton
                    onDropAcceptedFiles={(files) => {
                      setImageSourceType("upload");
                      setFiles(files);
                    }}
                  />
                )
              }
            />

            {!!file && (
              <>
                <FormField
                  error={
                    isFileExceeded &&
                    "This file is too large. Please choose one that's smaller than 10MB."
                  }
                  label="Effect type"
                  control={(props) => (
                    <SegmentedControl
                      {...props}
                      value={flipType}
                      onChange={setFlipType}
                      options={flipTypeOptions}
                    />
                  )}
                />

                {flipType === "mirror" && (
                  <>
                    <FormField
                      label="Mirror position"
                      control={(props) => (
                        <SegmentedControl
                          {...props}
                          value={mirrorPosition}
                          onChange={setMirrorPosition}
                          options={positionOptions}
                        />
                      )}
                    />

                    <FormField
                      label={`Offset: ${mirrorOffset}px`}
                      control={(props) => (
                        <Slider
                          {...props}
                          min={0}
                          max={100}
                          value={mirrorOffset}
                          onChange={setMirrorOffset}
                        />
                      )}
                    />

                    <FormField
                      label={`Opacity: ${mirrorOpacity}%`}
                      control={(props) => (
                        <Slider
                          {...props}
                          min={0}
                          max={100}
                          value={mirrorOpacity}
                          onChange={setMirrorOpacity}
                        />
                      )}
                    />
                  </>
                )}
              </>
            )}
            {!!file && (
              <Button
                stretch
                variant="primary"
                type="submit"
                disabled={!file || isFileExceeded}
                onClick={() => mutateAsync({
                  file,
                  flipType,
                  mirrorPosition,
                  mirrorOffset,
                  mirrorOpacity
                })}
              >
                Create Mirror Effect
              </Button>
            )}
            {processImageError && (
              <Alert tone="critical">{processImageError.message}</Alert>
            )}
          </>
        </Rows>
      )}
    </div>
  );
};
